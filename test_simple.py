#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

# Import from utils.test to avoid conflict with built-in test module
from utils.test import encrypt_c1c3c2

def test_encryption():
    """Test the SM2 encryption function"""
    try:
        data = "Hello from Java"
        public_key = "04BAE7ADE4862DB210BE1715C09D380218F6D4D32CA8CDBF50355C2A2D87A3191601CCA45626608499C7DEACE9D20D22ADE7CB6F255BA0B3B97520B17AD99F72DC"

        print(f"Testing encryption of: {data}")
        print(f"Public key: {public_key}")

        # Test encryption
        encrypted_bytes = encrypt_c1c3c2(data.encode('utf-8'), public_key)
        encrypted_hex = encrypted_bytes.hex().upper()

        print(f"Encrypted (hex): {encrypted_hex}")
        print(f"Encrypted length: {len(encrypted_hex)} characters")
        print(f"Encrypted bytes length: {len(encrypted_bytes)} bytes")

        # Test multiple encryptions to verify randomness
        print("\nTesting randomness (should produce different results):")
        for i in range(3):
            encrypted_bytes_2 = encrypt_c1c3c2(data.encode('utf-8'), public_key)
            encrypted_hex_2 = encrypted_bytes_2.hex().upper()
            print(f"Encryption {i+1}: {encrypted_hex_2[:32]}...")

        print("\n✅ All tests passed!")
        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_encryption()
