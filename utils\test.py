from gmssl import sm2




from gmssl import sm2, func

def encrypt_c1c3c222(data: bytes, public_key_hex: str) -> bytes:

    # 初始化 SM2 对象
    sm2_cipher = sm2.CryptSM2(public_key=public_key_hex, private_key="")

    # 生成随机私钥（仅用于计算共享密钥，不使用其私钥解密）
    sk = func.random_hex(64)

    # 获取公钥点 Q = (x, y)
    qx = int(public_key_hex[2:66], 16)
    qy = int(public_key_hex[66:], 16)

    # 生成临时密钥对（用于 ECDH）
    tmp_pk, tmp_sk = sm2.gen_key_pair(True)

    # 计算共享密钥并进行 KDF
    x, y = sm2.sm2_get_zy(tmp_sk, bytes.fromhex(public_key_hex))
    kdf_key = sm2.kdf(x, len(data))

    # 异或加密明文
    cipher_data = bytes([data[i] ^ kdf_key[i] for i in range(len(data))])

    # 拼接摘要（SM3(C1 || data)）
    c3 = sm2.sm3_hash(func.bytes_to_list(tmp_pk + data))

    # 返回 C1C3C2 格式
    return bytes.fromhex(tmp_pk) + bytes.fromhex(c3) + cipher_data





def encrypt_c1c3c2(data: bytes, public_key_hex: str) -> bytes:
    """
    使用 SM2 公钥加密数据，并返回 C1C3C2 格式密文（与 Hutool 一致）
    """
    # 使用 gmssl 库的标准加密方法
    sm2_cipher = sm2.CryptSM2(public_key=public_key_hex, private_key="")

    # 直接使用 gmssl 的加密功能
    encrypted_data = sm2_cipher.encrypt(data)

    if encrypted_data is None:
        raise ValueError("Encryption failed")

    return encrypted_data



data = "Hello from Java"
raw_pubkey_hex = "04BAE7ADE4862DB210BE1715C09D380218F6D4D32CA8CDBF50355C2A2D87A3191601CCA45626608499C7DEACE9D20D22ADE7CB6F255BA0B3B97520B17AD99F72DC"

cipher_bytes = encrypt_c1c3c2(data.encode('utf-8'), raw_pubkey_hex)
cipher_hex = cipher_bytes.hex().upper()

print(cipher_hex)